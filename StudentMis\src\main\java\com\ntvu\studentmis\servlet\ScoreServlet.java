package com.ntvu.studentmis.servlet;

import com.ntvu.studentmis.db.DBScore;
import com.ntvu.studentmis.entity.Score;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;



public class ScoreServlet extends HttpServlet {
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doPost(req,resp);
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        req.setCharacterEncoding("utf-8");
        String action = req.getParameter("action");
        System.out.println(action);

        String txtNum = req.getParameter("txtNum");
        String txtName = req.getParameter("txtName");
        String txtClass = req.getParameter("txtClass");
        String txtCourse = req.getParameter("txtCourse");
        String txtScore = req.getParameter("txtScore");
        String txtMajor = req.getParameter("txtMajor");

        switch(action)
        {
            case "add":
            {
                Score score = new Score();

                score.setStu_num(txtNum);
                score.setStu_name(txtName);
                score.setStu_class(txtClass);
                score.setCourse_name(txtCourse);
                score.setScore_grade(Double.parseDouble(txtScore));
                score.setMajor(txtMajor);

                DBScore db = new DBScore();
                boolean succeed = db.register(score);
                if(succeed)
                {
                    resp.sendRedirect(req.getContextPath() + "/admin/score/list.jsp");
                }else{
                    resp.sendRedirect(req.getContextPath() + "/admin/score/add.jsp");
                }
                return;
            }

            case "edit":
            {

                String id = req.getParameter("id");
                System.out.println("id:"+id);
                Score score = new Score();
                score.setScore_id(Integer.parseInt(id));
                score.setStu_num(txtNum);
                score.setStu_name(txtName);
                score.setStu_class(txtClass);
                score.setCourse_name(txtCourse);
                score.setScore_grade(Double.parseDouble(txtScore));
                score.setMajor(txtMajor);

                DBScore db = new DBScore();
                boolean succeed = db.edit(score);
                if(succeed)
                {
                    resp.sendRedirect(req.getContextPath() + "/admin/score/list.jsp");
                }else{
                    resp.sendRedirect(req.getContextPath() + "/admin/score/edit.jsp?id="+id);
                }
                return;
            }
            case "delete":
            {
                String id = req.getParameter("id");
                System.out.println(id);
                boolean succeed = new DBScore().delete(Integer.parseInt(id));
                if(succeed)
                {
                    System.out.println("成功");
                }else{
                    System.out.println("失败");
                }
                resp.sendRedirect(req.getContextPath() + "/admin/score/list.jsp");
                break;
            }
            case "deleteSelected":
            {
                String ids = req.getParameter("ids");
                String []arr = ids.split(",");
                boolean succeed = false;
                for(String s : arr) {
                    if(s == null || s.equals(""))
                    {
                        continue;
                    }
                    succeed = new DBScore().delete(Integer.parseInt(s));
                }
                if(succeed)
                {
                    System.out.println("成功");
                }else{
                    System.out.println("失败");
                }
                resp.sendRedirect(req.getContextPath() + "/admin/score/list.jsp");
                break;
            }
            default: {
                break;
            }
        }
        System.out.println(action);
    }
}
