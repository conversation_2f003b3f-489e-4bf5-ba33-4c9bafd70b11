package com.ntvu.studentmis.servlet;

import com.ntvu.studentmis.db.DBStudent;
import com.ntvu.studentmis.entity.Student;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;



public class StudentServlet extends HttpServlet {
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doPost(req,resp);
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        req.setCharacterEncoding("utf-8");
        String action = req.getParameter("action");
        System.out.println(action);

        String txtNum = req.getParameter("txtNum");
        String txtName = req.getParameter("txtName");
        String rdSex = req.getParameter("rdSex");
        String txtAge = req.getParameter("txtAge");
        String txtClass = req.getParameter("txtClass");
        String txtMajor = req.getParameter("txtMajor");
        String txtDepart = req.getParameter("txtDepart");

        switch(action)
        {
            case "add":
            {
                Student student = new Student();
                student.setStu_num(txtNum);
                student.setStu_name(txtName);
                student.setStu_sex(rdSex);
                student.setStu_age(Integer.parseInt(txtAge));
                student.setStu_class(txtClass);
                student.setMajor(txtMajor);
                student.setDepartment(txtDepart);
                DBStudent db = new DBStudent();
                boolean succeed = db.register(student);
                if(succeed)
                {
                    resp.sendRedirect(req.getContextPath() + "/admin/student/list.jsp");
                }else{
                    resp.sendRedirect(req.getContextPath() + "/admin/student/add.jsp");
                }
                return;
            }

            case "edit":
            {

                String id = req.getParameter("id");
                System.out.println("id:"+id);
                Student student = new Student();
                student.setStu_id(Integer.parseInt(id));
                student.setStu_num(txtNum);
                student.setStu_name(txtName);
                student.setStu_sex(rdSex);
                student.setStu_age(Integer.parseInt(txtAge));
                student.setStu_class(txtClass);
                student.setMajor(txtMajor);
                student.setDepartment(txtDepart);

                DBStudent db = new DBStudent();
                boolean succeed = db.edit(student);
                if(succeed)
                {
                    resp.sendRedirect(req.getContextPath() + "/admin/student/list.jsp");
                }else{
                    resp.sendRedirect(req.getContextPath() + "/admin/student/edit.jsp?id="+id);
                }
                return;
            }
            case "delete":
            {
                String id = req.getParameter("id");
                System.out.println(id);
                boolean succeed = new DBStudent().delete(Integer.parseInt(id));
                if(succeed)
                {
                    System.out.println("成功");
                }else{
                    System.out.println("失败");
                }
                resp.sendRedirect(req.getContextPath() + "/admin/student/list.jsp");
                break;
            }
            case "deleteSelected":
            {
                String ids = req.getParameter("ids");
                String []arr = ids.split(",");
                boolean succeed = false;
                for(String s : arr) {
                    if(s == null || s.equals(""))
                    {
                        continue;
                    }
                    succeed = new DBStudent().delete(Integer.parseInt(s));
                }
                if(succeed)
                {
                    System.out.println("成功");
                }else{
                    System.out.println("失败");
                }
                resp.sendRedirect(req.getContextPath() + "/admin/student/list.jsp");
                break;
            }
            default: {
                break;
            }
        }
        System.out.println(action);
    }
}
