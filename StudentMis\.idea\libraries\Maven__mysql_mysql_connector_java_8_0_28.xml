<component name="libraryTable">
  <library name="Maven: mysql:mysql-connector-java:8.0.28">
    <CLASSES>
      <root url="jar://E:/Work/maven_lib/repository/mysql/mysql-connector-java/8.0.28/mysql-connector-java-8.0.28.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://E:/Work/maven_lib/repository/mysql/mysql-connector-java/8.0.28/mysql-connector-java-8.0.28-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://E:/Work/maven_lib/repository/mysql/mysql-connector-java/8.0.28/mysql-connector-java-8.0.28-sources.jar!/" />
    </SOURCES>
  </library>
</component>