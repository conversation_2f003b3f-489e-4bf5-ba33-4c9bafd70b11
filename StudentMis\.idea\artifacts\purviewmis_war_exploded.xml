<component name="ArtifactManager">
  <artifact type="exploded-war" name="purviewmis:war exploded">
    <output-path>$PROJECT_DIR$/target/studentmis-1.0-SNAPSHOT</output-path>
    <properties id="maven-jee-properties">
      <options>
        <exploded>true</exploded>
        <module>purviewmis</module>
        <packaging>war</packaging>
      </options>
    </properties>
    <root id="root">
      <element id="directory" name="WEB-INF">
        <element id="directory" name="classes">
          <element id="module-output" name="purviewmis" />
        </element>
        <element id="directory" name="lib">
          <element id="library" level="project" name="Maven: mysql:mysql-connector-java:8.0.28" />
          <element id="library" level="project" name="Maven: com.google.protobuf:protobuf-java:3.11.4" />
          <element id="library" level="project" name="Maven: net.sourceforge.jexcelapi:jxl:2.6.12" />
          <element id="library" level="project" name="Maven: log4j:log4j:1.2.14" />
        </element>
      </element>
      <element id="directory" name="META-INF">
        <element id="file-copy" path="$PROJECT_DIR$/target/studentmis-1.0-SNAPSHOT/META-INF/MANIFEST.MF" />
      </element>
      <element id="javaee-facet-resources" facet="purviewmis/web/Web" />
    </root>
  </artifact>
</component>