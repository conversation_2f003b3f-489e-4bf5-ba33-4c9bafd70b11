<component name="libraryTable">
  <library name="Maven: org.junit.jupiter:junit-jupiter-engine:5.7.0">
    <CLASSES>
      <root url="jar://E:/Work/maven_lib/repository/org/junit/jupiter/junit-jupiter-engine/5.7.0/junit-jupiter-engine-5.7.0.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://E:/Work/maven_lib/repository/org/junit/jupiter/junit-jupiter-engine/5.7.0/junit-jupiter-engine-5.7.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://E:/Work/maven_lib/repository/org/junit/jupiter/junit-jupiter-engine/5.7.0/junit-jupiter-engine-5.7.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>