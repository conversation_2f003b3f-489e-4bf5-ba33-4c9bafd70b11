package com.ntvu.studentmis.servlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class LogoutServlet extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        //记录注销日志
        Object obj = req.getSession().getAttribute("curUserName");

        //跳转
        req.getSession().removeAttribute("curUserName");
        resp.sendRedirect("login.jsp");
    }
}
