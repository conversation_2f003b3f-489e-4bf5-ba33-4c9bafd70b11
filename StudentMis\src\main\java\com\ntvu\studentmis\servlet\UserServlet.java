package com.ntvu.studentmis.servlet;

import com.ntvu.studentmis.db.DBManager;
import com.ntvu.studentmis.entity.User;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.PrintWriter;


public class UserServlet extends HttpServlet {
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doPost(req,resp);
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        req.setCharacterEncoding("utf-8");
        String action = req.getParameter("action");
        System.out.println(action);
        String txtRole = req.getParameter("selectList");
        String txtLoginName = req.getParameter("txtLoginName");
        String txtLoginPassword = req.getParameter("txtLoginPassword");
        String txtTelephone = req.getParameter("txtTelephone");
        String txtRealName = req.getParameter("txtRealName");

        switch(action)
        {
            case "add":
            {
                User user = new User();
                user.setUser_num(txtLoginName);
                user.setPassword(txtLoginPassword);
                user.setUser_name(txtRealName);
                user.setPhone(txtTelephone);
                user.setRole_id(Integer.parseInt(txtRole));

                DBManager db = new DBManager();
                boolean succeed = db.register(user);
                if(succeed)
                {
                    resp.sendRedirect(req.getContextPath() + "/admin/user/list.jsp");
                }else{
                    resp.sendRedirect(req.getContextPath() + "/admin/user/add.jsp");
                }
                return;
            }

            case "edit":
            {

                String id = req.getParameter("id");
                System.out.println("id:"+id);
                User user = new User();
                user.setUser_id(Integer.parseInt(id));
                user.setUser_num(txtLoginName);
                user.setPassword(txtLoginPassword);
                user.setUser_name(txtRealName);
                user.setPhone(txtTelephone);
                user.setRole_id(Integer.parseInt(txtRole));

                DBManager db = new DBManager();
                boolean succeed = db.edit(user);
                if(succeed)
                {
                    resp.sendRedirect(req.getContextPath() + "/admin/user/list.jsp");
                }else{
                    resp.sendRedirect(req.getContextPath() + "/admin/user/edit.jsp?id="+id);
                }
                return;
            }
            case "forgot_password":
            {
                User user = new DBManager().getDetails(txtLoginName);
                System.out.println(txtLoginName);
                String password= user.getPassword();
                HttpSession session = req.getSession();
                resp.sendRedirect(req.getContextPath() + "/forgot_password.jsp");
                session.setAttribute("txtLoginName",txtLoginName);
                session.setAttribute("password",password);

                break;
            }
            case "delete":
            {
                String id = req.getParameter("id");
                System.out.println(id);
                boolean succeed = new DBManager().delete(Integer.parseInt(id));
                if(succeed)
                {
                    System.out.println("成功");
                }else{
                    System.out.println("失败");
                }
                resp.sendRedirect(req.getContextPath() + "/admin/user/list.jsp");
                break;
            }
            case "deleteSelected":
            {
                String ids = req.getParameter("ids");
                String []arr = ids.split(",");
                boolean succeed = false;
                for(String s : arr) {
                    if(s == null || s.equals(""))
                    {
                        continue;
                    }
                    succeed = new DBManager().delete(Integer.parseInt(s));
                }
                if(succeed)
                {
                    System.out.println("成功");
                }else{
                    System.out.println("失败");
                }
                resp.sendRedirect(req.getContextPath() + "/admin/user/list.jsp");
                break;
            }
            case "register":
            {
                DBManager db = new DBManager();
                User user=db.getDetails(txtLoginName);
                if (user != null) {
                    resp.sendRedirect(req.getContextPath() + "/register.jsp?id="+user.getUser_num()+user.getPhone());
                    return;
                }
                user = new User();
                user.setUser_num(txtLoginName);
                user.setPassword(txtLoginPassword);
                user.setUser_name(txtRealName);
                user.setPhone(txtTelephone);
                user.setRole_id(Integer.parseInt(txtRole));

                boolean succeed = db.register(user);
                if(succeed)
                {
                    resp.sendRedirect(req.getContextPath() + "/login.jsp");
                }else{
                    resp.sendRedirect(req.getContextPath() + "/register.jsp");
                }
                return;
            }
            default: {
                break;
            }
        }
        System.out.println(action);
    }
}
