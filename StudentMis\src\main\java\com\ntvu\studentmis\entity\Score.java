package com.ntvu.studentmis.entity;

public class Score {
    private Integer score_id;
    private String stu_num;
    private String stu_name;
    private  String stu_sex;
    private String course_name;
    private String stu_class;
    private Double score_grade;
    private String major;
    private  Double sumScore;
    private  Double avgScore;
    private  Double gpa; // 绩点
    private  String gradeLevel; // 成绩等级 (A, A-, B+, B, B-, C+, C, C-, D, F)

    public Double getSumScore() {
        return sumScore;
    }

    public void setSumScore(Double sumScore) {
        this.sumScore = sumScore;
    }

    public Double getAvgScore() {
        return avgScore;
    }

    public void setAvgScore(Double avgScore) {
        this.avgScore = avgScore;
    }

    public Integer getScore_id() {
        return score_id;
    }

    public String getStu_sex() {
        return stu_sex;
    }

    public void setStu_sex(String stu_sex) {
        this.stu_sex = stu_sex;
    }

    public void setScore_id(Integer score_id) {
        this.score_id = score_id;
    }

    public String getStu_num() {
        return stu_num;
    }

    public void setStu_num(String stu_num) {
        this.stu_num = stu_num;
    }

    public String getStu_name() {
        return stu_name;
    }

    public void setStu_name(String stu_name) {
        this.stu_name = stu_name;
    }

    public String getCourse_name() {
        return course_name;
    }

    public void setCourse_name(String course_name) {
        this.course_name = course_name;
    }

    public String getStu_class() {
        return stu_class;
    }

    public void setStu_class(String stu_class) {
        this.stu_class = stu_class;
    }

    public Double getScore_grade() {
        return score_grade;
    }

    public void setScore_grade(Double score_grade) {
        this.score_grade = score_grade;
    }

    public String getMajor() {
        return major;
    }

    public void setMajor(String major) {
        this.major = major;
    }

    public Double getGpa() {
        return gpa;
    }

    public void setGpa(Double gpa) {
        this.gpa = gpa;
    }

    public String getGradeLevel() {
        return gradeLevel;
    }

    public void setGradeLevel(String gradeLevel) {
        this.gradeLevel = gradeLevel;
    }

    /**
     * 根据分数计算GPA (清华大学标准)
     */
    public static Double calculateGPA(Double score) {
        if (score == null) return 0.0;

        if (score >= 95) return 4.0;      // A
        else if (score >= 90) return 3.7; // A-
        else if (score >= 85) return 3.3; // B+
        else if (score >= 80) return 3.0; // B
        else if (score >= 75) return 2.7; // B-
        else if (score >= 70) return 2.3; // C+
        else if (score >= 65) return 2.0; // C
        else if (score >= 60) return 1.7; // C-
        else if (score >= 50) return 1.0; // D
        else return 0.0;                  // F
    }

    /**
     * 根据分数获取等级
     */
    public static String getGradeLevel(Double score) {
        if (score == null) return "F";

        if (score >= 95) return "A";
        else if (score >= 90) return "A-";
        else if (score >= 85) return "B+";
        else if (score >= 80) return "B";
        else if (score >= 75) return "B-";
        else if (score >= 70) return "C+";
        else if (score >= 65) return "C";
        else if (score >= 60) return "C-";
        else if (score >= 50) return "D";
        else return "F";
    }
}
