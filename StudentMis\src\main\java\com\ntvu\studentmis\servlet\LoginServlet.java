package com.ntvu.studentmis.servlet;

import com.ntvu.studentmis.db.DBManager;
import com.ntvu.studentmis.entity.User;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
    import javax.servlet.http.HttpServletRequest;
    import javax.servlet.http.HttpServletResponse;
    import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * 处理用户登录过程
 */
public class LoginServlet extends HttpServlet {


    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        // 设置编码
        req.setCharacterEncoding("UTF-8");
        resp.setCharacterEncoding("UTF-8");
        resp.setContentType("text/html;charset=UTF-8");

        String userName = req.getParameter("txtUserName");//从请求中获取参数
        String password = req.getParameter("txtPassword");//从请求中获取参数
        HttpSession session = req.getSession();

        // Debug information
        System.out.println("=== Login Debug Info ===");
        System.out.println("Username: " + userName);
        System.out.println("Password: " + password);

        // Database authentication
        boolean succeed = new DBManager().login(userName, password);//WebTools.md5(password)
        System.out.println("Login result: " + succeed);
        if(succeed)
        {
            //成功
            session.setAttribute("curUserName",userName);
            User user=new DBManager().getDetails(userName);
            // Role-based redirection
            if(user.getRole_id()==2)// Admin
            {
                System.out.println("Admin login successful");
                resp.sendRedirect("admin/user/list.jsp");
            }else if(user.getRole_id()==0) {// Student
                System.out.println("Student login successful");
                resp.sendRedirect("student/student.jsp");
            }else// Teacher
            {
                System.out.println("Teacher login successful");
                resp.sendRedirect("teacher/teacher.jsp");
            }
        }else{
            // Login failed - set error message first, then redirect
            session.setAttribute("errorMsg","Username or password is incorrect!");
            session.setAttribute("user_num",userName); // Keep username
            resp.sendRedirect("login.jsp");
            return; // Return directly on login failure
        }
        // Save data on successful login
        session.setAttribute("user_num",userName);
        session.setAttribute("password",""); // Don't save password
    }
}
