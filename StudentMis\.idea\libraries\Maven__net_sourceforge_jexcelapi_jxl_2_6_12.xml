<component name="libraryTable">
  <library name="Maven: net.sourceforge.jexcelapi:jxl:2.6.12">
    <CLASSES>
      <root url="jar://$PROJECT_DIR$/../../../Work/maven_lib/repository/net/sourceforge/jexcelapi/jxl/2.6.12/jxl-2.6.12.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$PROJECT_DIR$/../../../Work/maven_lib/repository/net/sourceforge/jexcelapi/jxl/2.6.12/jxl-2.6.12-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$PROJECT_DIR$/../../../Work/maven_lib/repository/net/sourceforge/jexcelapi/jxl/2.6.12/jxl-2.6.12-sources.jar!/" />
    </SOURCES>
  </library>
</component>