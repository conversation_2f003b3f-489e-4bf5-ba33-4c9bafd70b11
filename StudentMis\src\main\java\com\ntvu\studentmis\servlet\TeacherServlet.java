package com.ntvu.studentmis.servlet;

import com.ntvu.studentmis.db.DBTeacher;
import com.ntvu.studentmis.entity.Teacher;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;



public class TeacherServlet extends HttpServlet {
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doPost(req,resp);
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        req.setCharacterEncoding("utf-8");
        String action = req.getParameter("action");
        System.out.println(action);

        String txtNum = req.getParameter("txtNum");
        String txtName = req.getParameter("txtName");
        String rdSex = req.getParameter("rdSex");
        String txtAge = req.getParameter("txtAge");
        String txtCourse = req.getParameter("txtCourse");
        String txtMajor = req.getParameter("txtMajor");
        String txtDepart = req.getParameter("txtDepart");

        switch(action)
        {
            case "add":
            {
                Teacher teacher = new Teacher();
                teacher.setTea_num(txtNum);
                teacher.setTea_name(txtName);
                teacher.setTea_sex(rdSex);
                teacher.setTea_age(Integer.parseInt(txtAge));
                teacher.setTea_course(txtCourse);
                teacher.setMajor(txtMajor);
                teacher.setDepartment(txtDepart);

                DBTeacher db = new DBTeacher();
                boolean succeed = db.register(teacher);
                if(succeed)
                {
                    resp.sendRedirect(req.getContextPath() + "/admin/teacher/list.jsp");
                }else{
                    resp.sendRedirect(req.getContextPath() + "/admin/teacher/add.jsp");
                }
                return;
            }

            case "edit":
            {

                String id = req.getParameter("id");
                System.out.println("id:"+id);
                Teacher teacher = new Teacher();

                teacher.setTea_id(Integer.parseInt(id));
                teacher.setTea_num(txtNum);
                teacher.setTea_name(txtName);
                teacher.setTea_sex(rdSex);
                teacher.setTea_age(Integer.parseInt(txtAge));
                teacher.setTea_course(txtCourse);
                teacher.setMajor(txtMajor);
                teacher.setDepartment(txtDepart);

                DBTeacher db = new DBTeacher();
                boolean succeed = db.edit(teacher);
                if(succeed)
                {
                    resp.sendRedirect(req.getContextPath() + "/admin/teacher/list.jsp");
                }else{
                    resp.sendRedirect(req.getContextPath() + "/admin/teacher/edit.jsp?id="+id);
                }
                return;
            }
            case "delete":
            {
                String id = req.getParameter("id");
                System.out.println(id);
                boolean succeed = new DBTeacher().delete(Integer.parseInt(id));
                if(succeed)
                {
                    System.out.println("成功");
                }else{
                    System.out.println("失败");
                }
                resp.sendRedirect(req.getContextPath() + "/admin/teacher/list.jsp");
                break;
            }
            case "deleteSelected":
            {
                String ids = req.getParameter("ids");
                String []arr = ids.split(",");
                boolean succeed = false;
                for(String s : arr) {
                    if(s == null || s.equals(""))
                    {
                        continue;
                    }
                    succeed = new DBTeacher().delete(Integer.parseInt(s));
                }
                if(succeed)
                {
                    System.out.println("成功");
                }else{
                    System.out.println("失败");
                }
                resp.sendRedirect(req.getContextPath() + "/admin/teacher/list.jsp");
                break;
            }
            default: {
                break;
            }
        }
        System.out.println(action);
    }
}
