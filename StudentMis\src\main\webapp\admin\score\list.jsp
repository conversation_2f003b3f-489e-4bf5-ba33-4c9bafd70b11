<%@ page import="com.ntvu.studentmis.pager.PagerHelper" %>
<%@ page import="com.ntvu.studentmis.entity.Score" %>
<%@ page import="com.ntvu.studentmis.util.WebTools" %>
<%@ page import="com.ntvu.studentmis.db.DBScore" %>
<%@ page import="jxl.write.WritableWorkbook" %>
<%@ page import="jxl.Workbook" %>
<%@ page import="java.io.File" %>
<%@ page import="jxl.write.WritableSheet" %>
<%@ page import="jxl.write.Label" %>
<%@ page import="jxl.write.WriteException" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <jsp:include page="../../include/header_css.jsp" flush="true"/>
    <title>学生成绩列表</title>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">
    <%@include file="../../include/header_nav.jsp"%>
    <%@include file="../../include/left_menu.jsp"%>
    <%
        request.setCharacterEncoding("utf-8");
        String stu_num = request.getParameter("stu_num");
        String stu_name = request.getParameter("stu_name");
        PagerHelper<Score> pager = new PagerHelper(request);
        if (stu_num != null && !stu_num.trim().equals("")) {
            pager.getQueryParams().put("stu_num", stu_num);
        }
        if (stu_name != null && !stu_name.trim().equals("")) {
            pager.getQueryParams().put("stu_name", stu_name);
        }
        new DBScore().getList(pager);
        double sumScore=pager.getSumScore();
        double avgScore=pager.getAvgScore();
    %>
    <!-- Content Wrapper. Contains page content -->
    <form id="form1" name="form1" method="post"
          action="<%= request.getContextPath() + "/admin/score/ScoreServlet?action=add"%>">
        <div class="content-wrapper">
            <section class="content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <div class="row">

                                        <div class="col-lg-2">学号：<input type="text" name="stu_num"
                                                                          class="form-control" value="<%= WebTools.parseNullorEmpty(stu_num)%>"></div>
                                        <div class="col-lg-2">姓名：<input type="text" name="stu_name"
                                                                          class="form-control" value="<%= WebTools.parseNullorEmpty(stu_name)%>"></div>
                                        <div class="col-lg-2">
                                            <input type="button" class="btn btn-block btn-primary btn-xs;form-control"
                                                    style="margin-top: 25px;" name="btnFind" value="查询">
                                        </div>
                                        <div class="col-lg-2">
                                            <input type="button" class="btn btn-block btn-danger btn-xs;form-control"
                                                    style="margin-top: 25px;" name="btnDelSel" value="删除所勾选的">
                                        </div>
                                        <div class="col-lg-2">此学生总成绩：<input type="text" class="form-control" disabled="disabled" value="<%= sumScore==0.0?"":sumScore%>" placeholder="点击查询后显示"></div>
                                        <div class="col-lg-2">此学生平均分：<input type="text" class="form-control" disabled="disabled" value="<%= avgScore==0.0?"":avgScore%>" placeholder="点击查询后显示"></div>
                                        <div class="col-lg-2">平均绩点：<input type="text" class="form-control" disabled="disabled" value="<%= avgScore==0.0?"":String.format("%.2f", com.ntvu.studentmis.entity.Score.calculateGPA(avgScore))%>" placeholder="点击查询后显示" style="color: #007bff; font-weight: bold;"></div>
                                    </div>
                                </div>

                                <!-- 学习进度图表区域 -->
                                <div class="card">
                                    <div class="card-header">
                                        <h3 class="card-title">
                                            <i class="fas fa-chart-line mr-1"></i>
                                            学习进度分析
                                        </h3>
                                    </div>
                                    <div class="card-body">
                                        <div class="row mb-4">
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h3 class="card-title">
                                                            <i class="fas fa-chart-pie mr-1"></i>
                                                            当前学习进度
                                                        </h3>
                                                    </div>
                                                    <div class="card-body">
                                                        <canvas id="currentProgressChart" style="height: 300px; max-height: 300px;"></canvas>
                                                        <div class="mt-3">
                                                            <div class="progress-group">
                                                                已完成学分：<span id="completedCredits" class="float-right"><b>0</b>/160</span>
                                                                <div class="progress progress-sm">
                                                                    <div id="creditProgressBar" class="progress-bar bg-primary" style="width: 0%"></div>
                                                                </div>
                                                            </div>
                                                            <div class="progress-group">
                                                                当前GPA：<span id="currentGPA" class="float-right"><b>0.0</b>/4.0</span>
                                                                <div class="progress progress-sm">
                                                                    <div id="gpaProgressBar" class="progress-bar bg-success" style="width: 0%"></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h3 class="card-title">
                                                            <i class="fas fa-chart-bar mr-1"></i>
                                                            毕业进度分析
                                                        </h3>
                                                    </div>
                                                    <div class="card-body">
                                                        <canvas id="graduationProgressChart" style="height: 300px; max-height: 300px;"></canvas>
                                                        <div class="mt-3">
                                                            <div class="info-box">
                                                                <span class="info-box-icon bg-info"><i class="fas fa-graduation-cap"></i></span>
                                                                <div class="info-box-content">
                                                                    <span class="info-box-text">毕业进度</span>
                                                                    <span id="graduationPercentage" class="info-box-number">0%</span>
                                                                    <div class="progress">
                                                                        <div id="graduationProgressBar" class="progress-bar bg-info" style="width: 0%"></div>
                                                                    </div>
                                                                    <span id="graduationStatus" class="progress-description">距离毕业还需努力</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 成绩表格区域 -->
                                <div class="card">
                                    <div class="card-header">
                                        <h3 class="card-title">
                                            <i class="fas fa-table mr-1"></i>
                                            成绩详细列表
                                        </h3>
                                    </div>
                                    <!-- /.card-header -->
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <table id="example1"
                                                       class="table table-bordered table-striped dataTable dtr-inline"
                                                       aria-describedby="example1_info">
                                                    <thead>
                                                    <tr>
                                                        <th class="sorting sorting_asc" tabindex="0"
                                                            aria-controls="example1" rowspan="1" colspan="1"
                                                            aria-sort="ascending"
                                                            aria-label="Rendering engine: activate to sort column descending">
                                                            <input type="checkbox" class="form-check" title="全选" name="checkAll">
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Platform(s): activate to sort column ascending">
                                                            序号
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Browser: activate to sort column ascending">
                                                            学号
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Platform(s): activate to sort column ascending">
                                                            姓名
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Platform(s): activate to sort column ascending">
                                                            性别
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            班级
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            科目
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            成绩
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="GPA: activate to sort column ascending">
                                                            绩点
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Grade Level: activate to sort column ascending">
                                                            等级
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            专业
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            操作
                                                        </th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <%

                                                        int index = 0;
                                                        for (Score score : pager.getData()) {
                                                            index++;
                                                    %>
                                                    <tr class="<%= index % 2 == 1 ? "odd" : "even"%>">
                                                        <td><input type="checkbox" class="form-check" name="checkItem" value="<%= score.getScore_id()%>"></td>
                                                        <td><%= index%></td>
                                                        <td class="dtr-control sorting_1"
                                                            tabindex="0"><%= score.getStu_num()%>
                                                        </td>
                                                        <td><%= score.getStu_name()%>
                                                        </td>
                                                        <td><%= score.getStu_sex()%>
                                                        </td>
                                                        <td><%= score.getStu_class()%>
                                                        </td>
                                                        <td><%= score.getCourse_name()%>
                                                        </td>
                                                        <td><%= score.getScore_grade()%></td>
                                                        <td class="gpa-cell">
                                                            <span class="gpa-value"><%= String.format("%.1f", com.ntvu.studentmis.entity.Score.calculateGPA(score.getScore_grade()))%></span>
                                                        </td>
                                                        <td class="grade-level-cell">
                                                            <span class="grade-badge grade-<%= com.ntvu.studentmis.entity.Score.getGradeLevel(score.getScore_grade()).toLowerCase().replace("+", "plus").replace("-", "minus")%>">
                                                                <%= com.ntvu.studentmis.entity.Score.getGradeLevel(score.getScore_grade())%>
                                                            </span>
                                                        </td>
                                                        <td><%= score.getMajor()%></td>
                                                        <td style="width: 200px">
                                                            <div class="row">
                                                                <div class="col-6">
                                                                    <button type="button"
                                                                            class="btn btn-block btn-primary btn-xs"
                                                                            style="width: 80px" onclick="window.location.href = '<%= contextPath +"/admin/score/edit.jsp?id=" + score.getScore_id()%>';">编辑
                                                                    </button>
                                                                </div>
                                                                <div class="col-6">
                                                                    <button type="button"
                                                                            class="btn btn-block btn-danger btn-xs"
                                                                            style="width: 80px" onclick="if(confirm('当前操作不可恢复，确认删除吗？')){
                                                                            window.location.href='<%= contextPath +"/admin/score/ScoreServlet?action=delete&id=" + score.getScore_id()%>';}">删除
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <%
                                                        }
                                                    %>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-5">
                                                <div class="dataTables_info" id="example1_info" role="status"
                                                     aria-live="polite">每页显示10条记录
                                                </div>
                                            </div>
                                            <div class="col-sm-12 col-md-7">
                                                <%@ include file="../../include/pager_footer.jsp"%>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- /.card-body -->
                            </div>
                            <!-- /.card -->
                        </div>
                        <!-- /.col -->
                    </div>
                    <!-- /.row -->
                </div>
                <!-- /.container-fluid -->
            </section>
        </div>
    </form>
</div>
<%@include file="../../include/foot_js.jsp"%>
<style>
    /* GPA和等级显示样式 */
    .gpa-cell {
        text-align: center;
        font-weight: bold;
    }

    .gpa-value {
        color: #007bff;
        font-size: 1.1em;
    }

    .grade-level-cell {
        text-align: center;
    }

    .grade-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.85em;
        font-weight: bold;
        text-align: center;
        min-width: 35px;
    }

    /* 等级颜色 */
    .grade-a {
        background-color: #28a745;
        color: white;
    }

    .grade-aminus {
        background-color: #20c997;
        color: white;
    }

    .grade-bplus {
        background-color: #17a2b8;
        color: white;
    }

    .grade-b {
        background-color: #6f42c1;
        color: white;
    }

    .grade-bminus {
        background-color: #6610f2;
        color: white;
    }

    .grade-cplus {
        background-color: #fd7e14;
        color: white;
    }

    .grade-c {
        background-color: #ffc107;
        color: #212529;
    }

    .grade-cminus {
        background-color: #e83e8c;
        color: white;
    }

    .grade-d {
        background-color: #dc3545;
        color: white;
    }

    .grade-f {
        background-color: #6c757d;
        color: white;
    }
</style>
<script>
    $(function (){
        // 初始化图表
        initializeCharts();

        //绑定勾选框按钮事件
        $('input[name=checkAll]').bind('change',function (){
            console.log('checkAll');
            let checked=$(this).prop('checked');
            //更改表格中所有chkItem
            $('input[name=checkItem]').each(function (){
                console.log('checkItem');
                $(this).prop('checked',checked);
            });
        });
        //绑定删除所有按钮事件
        $('input[name=btnDelSel]').bind('click',function (){
            let ids='';
            $('input[name=checkItem]').each(function (){
                if( $(this).prop('checked')===true)
                {
                    ids+=$(this).val()+',';
                }
            });
            if(ids.length>0)
            {
                if(confirm('当前操作不可恢复,确认要删除吗?'))
                {
                    console.log(ids);
                    window.location.href='<%=contextPath+"/admin/score/ScoreServlet?action=deleteSelected&ids="%>'+ids;
                }
            }else {
                alert('请选择待删除项');
            }

        });
        $('input[name=btnFind]').bind('click',function (){
            $('#form1').attr('action','<%= request.getContextPath() + "/admin/score/list.jsp"%>');
            $(`#form1`).submit();
        });
    });
    /**
     * 跳转到指定的页
     * @param toPageIndex
     */
        //本页地址
    let pageListUrl = '/admin/score/list.jsp';
    function doPager(toPageIndex)
    {
        $('#form1').attr('action','<%= request.getContextPath() %>' + pageListUrl + '?pageIndex=' + toPageIndex);
        $('#form1').submit();
    }

    // 初始化图表
    function initializeCharts() {
        // 获取成绩数据
        var scoreData = [];
        <%
        for (Score score : pager.getData()) {
        %>
        scoreData.push({
            stuNum: '<%= score.getStu_num()%>',
            stuName: '<%= score.getStu_name()%>',
            courseName: '<%= score.getCourse_name()%>',
            score: <%= score.getScore_grade()%>,
            major: '<%= score.getMajor()%>'
        });
        <%
        }
        %>

        // 计算学习进度数据
        var progressData = calculateProgressData(scoreData);

        // 渲染图表
        renderCurrentProgressChart(progressData);
        renderGraduationProgressChart(progressData);

        // 更新进度显示
        updateProgressDisplay(progressData);
    }

    // 计算进度数据
    function calculateProgressData(scoreData) {
        var totalCredits = 160; // 毕业要求总学分
        var totalCourses = 40;   // 毕业要求总课程数
        var minGPA = 2.0;        // 最低GPA要求

        // 按学生分组统计
        var studentStats = {};
        scoreData.forEach(function(score) {
            if (!studentStats[score.stuNum]) {
                studentStats[score.stuNum] = {
                    name: score.stuName,
                    scores: [],
                    totalScore: 0,
                    passedCourses: 0,
                    failedCourses: 0,
                    completedCredits: 0
                };
            }

            var student = studentStats[score.stuNum];
            student.scores.push(score.score);
            student.totalScore += score.score;

            var credits = 3; // 假设每门课程3学分
            if (score.score >= 60) {
                student.passedCourses++;
                student.completedCredits += credits;
            } else {
                student.failedCourses++;
            }
        });

        // 计算整体统计
        var totalStudents = Object.keys(studentStats).length;
        var totalCompletedCredits = 0;
        var totalGPA = 0;
        var totalPassedCourses = 0;
        var totalFailedCourses = 0;

        Object.values(studentStats).forEach(function(student) {
            totalCompletedCredits += student.completedCredits;
            var avgScore = student.scores.length > 0 ? student.totalScore / student.scores.length : 0;
            totalGPA += convertScoreToGPA(avgScore);
            totalPassedCourses += student.passedCourses;
            totalFailedCourses += student.failedCourses;
        });

        var avgCompletedCredits = totalStudents > 0 ? totalCompletedCredits / totalStudents : 0;
        var avgGPA = totalStudents > 0 ? totalGPA / totalStudents : 0;
        var avgPassedCourses = totalStudents > 0 ? totalPassedCourses / totalStudents : 0;
        var avgFailedCourses = totalStudents > 0 ? totalFailedCourses / totalStudents : 0;

        // 计算各项进度百分比
        var creditProgress = Math.min(100, (avgCompletedCredits / totalCredits) * 100);
        var gpaProgress = Math.min(100, (avgGPA / 4.0) * 100);
        var courseProgress = Math.min(100, (avgPassedCourses / totalCourses) * 100);

        // 综合毕业进度
        var graduationProgress = Math.round(
            creditProgress * 0.4 +    // 学分完成度 40%
            gpaProgress * 0.3 +       // GPA达标度 30%
            courseProgress * 0.2 +    // 课程完成度 20%
            Math.max(0, 100 - avgFailedCourses * 5) * 0.1  // 挂科控制 10%
        );

        return {
            totalCredits: totalCredits,
            completedCredits: Math.round(avgCompletedCredits),
            remainingCredits: Math.round(totalCredits - avgCompletedCredits),
            currentGPA: avgGPA,
            passedCourses: Math.round(avgPassedCourses),
            failedCourses: Math.round(avgFailedCourses),
            creditProgress: creditProgress,
            gpaProgress: gpaProgress,
            courseProgress: courseProgress,
            graduationProgress: Math.min(100, graduationProgress),
            totalStudents: totalStudents
        };
    }

    // 分数转GPA
    function convertScoreToGPA(score) {
        if (score >= 90) return 4.0;
        if (score >= 85) return 3.7;
        if (score >= 82) return 3.3;
        if (score >= 78) return 3.0;
        if (score >= 75) return 2.7;
        if (score >= 72) return 2.3;
        if (score >= 68) return 2.0;
        if (score >= 64) return 1.7;
        if (score >= 60) return 1.0;
        return 0.0;
    }

    // 渲染当前进度饼图
    function renderCurrentProgressChart(data) {
        var ctx = document.getElementById('currentProgressChart').getContext('2d');

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['已完成学分', '剩余学分'],
                datasets: [{
                    data: [data.completedCredits, data.remainingCredits],
                    backgroundColor: ['#28a745', '#dc3545'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    position: 'bottom'
                },
                tooltips: {
                    callbacks: {
                        label: function(tooltipItem, data) {
                            var label = data.labels[tooltipItem.index] || '';
                            var value = data.datasets[0].data[tooltipItem.index];
                            var percentage = ((value / data.totalCredits) * 100).toFixed(1);
                            return label + ': ' + value + '学分 (' + percentage + '%)';
                        }
                    }
                }
            }
        });
    }

    // 渲染毕业进度柱状图
    function renderGraduationProgressChart(data) {
        var ctx = document.getElementById('graduationProgressChart').getContext('2d');

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['学分进度', 'GPA进度', '课程进度', '综合进度'],
                datasets: [{
                    label: '完成百分比',
                    data: [data.creditProgress, data.gpaProgress, data.courseProgress, data.graduationProgress],
                    backgroundColor: [
                        '#007bff',
                        '#28a745',
                        '#ffc107',
                        '#17a2b8'
                    ],
                    borderColor: [
                        '#0056b3',
                        '#1e7e34',
                        '#e0a800',
                        '#117a8b'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                aspectRatio: 2,
                legend: {
                    display: false
                },
                scales: {
                    yAxes: [{
                        ticks: {
                            beginAtZero: true,
                            max: 100,
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }]
                },
                tooltips: {
                    callbacks: {
                        label: function(tooltipItem, data) {
                            return data.datasets[0].label + ': ' + tooltipItem.yLabel.toFixed(1) + '%';
                        }
                    }
                }
            }
        });
    }

    // 更新进度显示
    function updateProgressDisplay(data) {
        // 更新学分进度
        $('#completedCredits').html('<b>' + data.completedCredits + '</b>/' + data.totalCredits);
        $('#creditProgressBar').css('width', data.creditProgress + '%');

        // 更新GPA进度
        $('#currentGPA').html('<b>' + data.currentGPA.toFixed(1) + '</b>/4.0');
        $('#gpaProgressBar').css('width', data.gpaProgress + '%');

        // 更新毕业进度
        $('#graduationPercentage').text(data.graduationProgress + '%');
        $('#graduationProgressBar').css('width', data.graduationProgress + '%');

        // 更新毕业状态文字
        var status = '';
        if (data.graduationProgress >= 90) {
            status = '整体进度优秀，即将毕业！';
        } else if (data.graduationProgress >= 75) {
            status = '整体进度良好，继续努力！';
        } else if (data.graduationProgress >= 50) {
            status = '整体进度正常，需要加油！';
        } else {
            status = '整体进度需要提升！';
        }
        $('#graduationStatus').text(status + ' (统计' + data.totalStudents + '名学生)');
    }
</script>

</body>
</html>
