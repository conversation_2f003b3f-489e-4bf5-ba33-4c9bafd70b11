<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <jsp:include page="../../include/header_css.jsp" flush="true"/>
    <title>添加学生</title>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">
    <%@include file="../../include/header_nav.jsp"%>
    <%@include file="../../include/left_menu.jsp"%>
    <div class="content-wrapper" style="min-height: 1345.6px;">
        <!-- Content Header (Page header) -->
        <section class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1>增加新用户</h1>
                    </div>
                </div>
            </div><!-- /.container-fluid -->
        </section>

        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">
                <div class="row">
                    <!-- left column -->
                    <div class="col-md-12">
                        <!-- jquery validation -->
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">请添加<small>新用户</small></h3>
                            </div>
                            <!-- /.card-header -->
                            <!-- form start -->
                            <form name="form1" id="form1" method="post" action="<%= request.getContextPath() + "/admin/user/UserServlet?action=add"%>" onsubmit="return verify()">
                                <div class="card-body">
                                    <div class="form-group">
                                        <label for="exampleInputPassword1">用户名</label>
                                        <input type="text" name="txtLoginName" class="form-control" id="exampleInputUser" placeholder="请输入用户名">
                                    </div>
                                    <div class="form-group">
                                        <label for="exampleInputPassword1">真实名称</label>
                                        <input type="text" name="txtRealName" class="form-control" id="exampleInputRealName" placeholder="请输入真实姓名">
                                    </div>
                                    <div class="form-group">
                                        <label for="exampleInputPassword1">密码</label>
                                        <input type="password" name="txtLoginPassword" class="form-control" id="exampleInputPassword1" placeholder="请输入密码"
                                               pattern="(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{6,}"
                                               title="密码必须包含至少一个大写字母、一个小写字母和一个数字，且长度至少6位"
                                               required>
                                    </div>
                                    <div class="form-group">
                                        <label for="exampleInputPassword1">确认密码</label>
                                        <input type="password" name="txtLoginPassword2" class="form-control" id="exampleInputPassword2" placeholder="请确认密码"
                                               pattern="(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{6,}"
                                               title="密码必须包含至少一个大写字母、一个小写字母和一个数字，且长度至少6位"
                                               required>
                                    </div>
                                    <div class="form-group">
                                        <label for="exampleInputPassword1">手机号</label>
                                        <input type="tel" name="txtTelephone" class="form-control" id="exampleInputTelephone" placeholder="请输入手机号"  pattern="^1[3-9]\d{9}$"
                                               title="请输入11位有效手机号（以13-19开头）"
                                               required >
                                    </div>
                                    <div class="form-group">
                                        <label for="exampleInputEmail1">身份</label>
                                        <select name="selectList" id="exampleInputEmail1">
                                            <option name="txtRole" value="">请选择身份</option>
                                            <option name="txtRole" value="0">学生</option>
                                            <option name="txtRole" value="1">教师</option>
                                            <option name="txtRole" value="2">管理员</option>
                                        </select>
                                    </div>
                                </div>
                                <!-- /.card-body -->
                                <div class="card-footer">
                                    <input type="submit" class="btn btn-primary" name="btnSubmit" value="提交">
                                </div>
                            </form>
                        </div>
                        <!-- /.card -->
                    </div>
                    <!--/.col (left) -->
                    <!-- right column -->
                    <div class="col-md-6">

                    </div>
                    <!--/.col (right) -->
                </div>
                <!-- /.row -->
            </div><!-- /.container-fluid -->
        </section>
        <!-- /.content -->
    </div>
</div>
<%@include file="../../include/foot_js.jsp"%>
<script>
    function verify()
    {
        console.log(`click`);
        //对数据进行检验
        let txtLoginName=$(`input[name=txtLoginName]`).val();
        if(txtLoginName==='')
        {
            alert(`登录名称不能为空`);
            $(`input[name=txtLoginName]`).focus();//光标选中
            return false;
        }
        let txtRealName=$(`input[name=txtRealName]`).val();
        if(txtRealName==='')
        {
            alert(`姓名不能为空`);
            $(`input[name=txtRealName]`).focus();//光标选中
            return false;
        }
        let txtLoginPassword2=$(`input[name=txtLoginPassword2]`).val();
        if(txtLoginPassword2==='')
        {
            alert(`确认密码不能为空`);
            $(`input[name=txtLoginPassword2]`).focus();//光标选中
            return false;
        }
        let txtLoginPassword=$(`input[name=txtLoginPassword]`).val();
        if(txtLoginPassword==='')
        {
            alert(`密码不能为空`);
            $(`input[name=txtLoginPassword]`).focus();//光标选中
            return false;
        }
        if(txtLoginPassword!==txtLoginPassword2)
        {
            alert(`两次密码必须相同`);
            $(`input[name=txtLoginPassword]`).focus();//光标选中
            $(`input[name=txtLoginPassword2]`).focus();//光标选中
            return false;
        }
        let txtTelephone=$(`input[name=txtTelephone]`).val();
        if(txtTelephone==='')
        {
            alert(`手机号不能为空`);
            $(`input[name=txtTelephone]`).focus();//光标选中
            return false;
        }
        let selectValue = $('select').val();
        if(selectValue==='')
        {
            alert(`请选择你的身份`);
            return false;
        }

    }
</script>

</body>
</html>
