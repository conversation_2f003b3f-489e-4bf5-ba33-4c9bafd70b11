package com.ntvu.studentmis.util;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 存放公用的方法
 */
public class WebTools {
    /**
     * 利用jdk MD5方法，完成对明文进行加密
     * @param oldContent
     * @return
     */
    public static String md5(String oldContent)
    {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");// 生成一个MD5加密计算摘要
            md.update(oldContent.getBytes());
            String hashedPwd = new BigInteger(1, md.digest()).toString(16);// 16是表示转换为16进制数
            System.out.println(hashedPwd);
            return hashedPwd;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    public  static String parseNullorEmpty(Object obj)
    {
        if(obj==null||obj.equals(""))
        {
            return "";
        }
        else {
            return obj.toString().trim();
        }
    }

    public static String dateToStr(java.util.Date dateDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = formatter.format(dateDate);
        return dateString;
    }
    /**
     * 将短时间格式字符串转换为时间 yyyy-MM-dd
     *
     * @param strDate
     * @return
     */
    public static Date strToDate(String strDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        ParsePosition pos = new ParsePosition(0);
        Date strtodate = formatter.parse(strDate, pos);
        return strtodate;
    }
}
