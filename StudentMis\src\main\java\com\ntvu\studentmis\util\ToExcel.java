package com.ntvu.studentmis.util;

import com.ntvu.studentmis.db.DBScore;
import com.ntvu.studentmis.entity.Score;
import com.ntvu.studentmis.pager.PagerHelper;
import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;

public class ToExcel extends HttpServlet {
    private static final long serialVersionUID = 1L;

    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // 从数据库中读取数据
        PagerHelper<Score> pager = new PagerHelper(request);
        new DBScore().getExcelData(pager);

        // 导出数据到 Excel 文件
        String fileName = "D:/data.xls";
        WritableWorkbook workbook = Workbook.createWorkbook(new File(fileName));
        WritableSheet sheet = workbook.createSheet("sheet1", 0);
        try {

            sheet.addCell(new Label(0, 0, "学号"));
            sheet.addCell(new Label(1, 0, "姓名"));
            sheet.addCell(new Label(2, 0, "性别"));
            sheet.addCell(new Label(3, 0, "科目"));
            sheet.addCell(new Label(4, 0, "成绩"));
            sheet.addCell(new Label(5, 0, "总分"));
            sheet.addCell(new Label(6, 0, "平均分"));
        }catch (Exception e){e.printStackTrace();}

        int i=0;
        for (Score score : pager.getData()) {
           try {
               sheet.addCell(new Label(0, i + 1, score.getStu_num()));
               sheet.addCell(new Label(1, i + 1, String.valueOf(score.getStu_name())));
               sheet.addCell(new Label(2, i + 1, score.getStu_sex()));
               sheet.addCell(new Label(3, i + 1, score.getCourse_name()));
               sheet.addCell(new Label(4, i + 1, String.valueOf(score.getScore_grade())));
               sheet.addCell(new Label(5, i + 1, String.valueOf(score.getSumScore())));
               sheet.addCell(new Label(6, i + 1, String.valueOf(score.getAvgScore())));
           }catch (Exception e){e.printStackTrace();}
            i++;
        }


        workbook.write();
        try {
            workbook.close();
        } catch (WriteException e) {
            e.printStackTrace();
        }

        // 下载导出的 Excel 文件
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment; filename=data.xls");
        String exportStatus = "success"; // 或根据实际情况设置为"fail"
        response.sendRedirect(request.getContextPath() + "/admin/user/list.jsp?export=" + exportStatus);
    }
}
