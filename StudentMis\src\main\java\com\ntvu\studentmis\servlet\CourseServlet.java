package com.ntvu.studentmis.servlet;

import com.ntvu.studentmis.db.DBCourse;
import com.ntvu.studentmis.entity.Course;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;


public class CourseServlet extends HttpServlet {
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doPost(req,resp);
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        req.setCharacterEncoding("utf-8");
        String action = req.getParameter("action");
        System.out.println(action);

        String txtName = req.getParameter("txtName");
        String txtCredit = req.getParameter("txtCredit");
        String txtHours = req.getParameter("txtHours");
        String txtTeacher = req.getParameter("txtTeacher");
        String txtDate = req.getParameter("txtDate");
        switch(action)
        {
            case "add":
            {
                Course course = new Course();

                course.setCourse_name(txtName);
                course.setCourse_credit(Integer.valueOf(txtCredit));
                course.setCourse_hours(Integer.valueOf(txtHours));
                course.setCourse_teacher(txtTeacher);
                course.setCoursedate(txtDate);

                DBCourse db = new DBCourse();
                boolean succeed = db.register(course);
                if(succeed)
                {
                    resp.sendRedirect(req.getContextPath() + "/admin/course/list.jsp");
                }else{
                    resp.sendRedirect(req.getContextPath() + "/admin/course/add.jsp");
                }
                return;
            }

            case "edit":
            {

                String id = req.getParameter("id");
                System.out.println("id:"+id);
                Course course = new Course();
                course.setCourse_id(Integer.parseInt(id));
                course.setCourse_name(txtName);
                course.setCourse_credit(Integer.valueOf(txtCredit));
                course.setCourse_hours(Integer.valueOf(txtHours));
                course.setCourse_teacher(txtTeacher);
                course.setCoursedate(txtDate);

                DBCourse db = new DBCourse();
                boolean succeed = db.edit(course);
                if(succeed)
                {
                    resp.sendRedirect(req.getContextPath() + "/admin/course/list.jsp");
                }else{
                    resp.sendRedirect(req.getContextPath() + "/admin/course/edit.jsp?id="+id);
                }
                return;
            }
            case "delete":
            {
                String id = req.getParameter("id");
                System.out.println(id);
                boolean succeed = new DBCourse().delete(Integer.parseInt(id));
                if(succeed)
                {
                    System.out.println("成功");
                }else{
                    System.out.println("失败");
                }
                resp.sendRedirect(req.getContextPath() + "/admin/course/list.jsp");
                break;
            }
            case "deleteSelected":
            {
                String ids = req.getParameter("ids");
                String []arr = ids.split(",");
                boolean succeed = false;
                for(String s : arr) {
                    if(s == null || s.equals(""))
                    {
                        continue;
                    }
                    succeed = new DBCourse().delete(Integer.parseInt(s));
                }
                if(succeed)
                {
                    System.out.println("成功");
                }else{
                    System.out.println("失败");
                }
                resp.sendRedirect(req.getContextPath() + "/admin/course/list.jsp");
                break;
            }
            default: {
                break;
            }
        }
        System.out.println(action);
    }
}
