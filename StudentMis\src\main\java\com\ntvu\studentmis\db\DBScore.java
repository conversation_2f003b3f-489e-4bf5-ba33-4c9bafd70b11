package com.ntvu.studentmis.db;

import com.ntvu.studentmis.entity.Score;
import com.ntvu.studentmis.pager.PagerHelper;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 对数据库操作的类
 */
public class DBScore {
    //驱动类
    private final String driverClassName = "com.mysql.cj.jdbc.Driver";
    //连接数据库地址
    private final String url = "*****************************************";
    //连接数据库用户名
    private final String dbName = "root";
    //连接数据库密码
    private final String dbPwd = "2847";

    //
    private Connection conn = null;
    private Statement stmt = null;
    private String sql = null;
    private ResultSet rs = null;

    /**
     * 初始化数据连接
     */
    private void init(){
        try {
            //加载驱动
            Class.forName(driverClassName);
            //获得与数据库的连接
            this.conn = DriverManager.getConnection(url,dbName,dbPwd);
            //获得招待句柄
            this.stmt = this.conn.createStatement();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 释放连接资源
     */
    private void release()
    {
        //关闭
        try {
            if(rs != null && !rs.isClosed())
            {
                rs.close();
                rs = null;
            }
            if(stmt != null && !stmt.isClosed()) {
                stmt.close();
                stmt = null;
            }
            if(conn != null && !conn.isClosed()) {
                conn.close();
                conn = null;
            }
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }
    }

    public boolean register(Score score)
    {
        boolean succeed = false;
        try{
            init();
            sql = "insert into Score(stu_num,stu_name,stu_sex,stu_class,course_name,score_grade,major)" +
                    " values(%s,%s,%s,%s,%s,%f,%s)";
            sql = String.format(sql,
                    "'" + score.getStu_num() + "'",
                    "'" + score.getStu_name() + "'",
                    "'" + score.getStu_sex() + "'",
                    "'" + score.getStu_class() + "'",
                    "'" + score.getCourse_name() + "'",
                     score.getScore_grade(),
                    "'" + score.getMajor() + "'");
            System.out.println(sql);
            //执行，返回受影响的记录行数
            int effectedRows = stmt.executeUpdate(sql);
            //使用结果
            //如果结果集不为空
            succeed = effectedRows > 0;
            //关闭
            release();
        }catch (Exception ex)
        {
            ex.printStackTrace();
        }
        return succeed;
    }

    public boolean delete(int id)
    {
        boolean succeed = false;
        try{
            init();
            sql = "delete from Score where score_id = %d";
            sql = String.format(sql, id);
            System.out.println(sql);
            //执行，返回受影响的记录行数
            int effectedRows = stmt.executeUpdate(sql);
            //使用结果
            if(effectedRows > 0)
            {
                //如果结果集不为空
                succeed = true;
            }else{
                succeed = false;
            }
            //关闭
            release();
        }catch (Exception ex)
        {
            ex.printStackTrace();
        }
        return succeed;
    }

    public boolean edit(Score score)
    {
        boolean succeed = false;
        try{
            init();
            sql = "update Score set stu_num=%s,stu_name=%s,stu_sex=%s,stu_class=%s,course_name=%s,score_grade=%f,major=%s where score_id = %d";
            sql = String.format(sql,
                    "'" + score.getStu_num() + "'",
                    "'" + score.getStu_name() + "'",
                    "'" + score.getStu_sex() + "'",
                    "'" + score.getStu_class() + "'",
                    "'" + score.getCourse_name() + "'",
                    score.getScore_grade(),
                    "'" + score.getMajor() + "'",
                    score.getScore_id());
            System.out.println(sql);
            //执行，返回受影响的记录行数
            int effectedRows = stmt.executeUpdate(sql);
            //使用结果
            //如果结果集不为空
            succeed = effectedRows > 0;
            //关闭
            release();
        }catch (Exception ex)
        {
            ex.printStackTrace();
        }
        return succeed;
    }

    //编辑删除
    public Score getListById(int id)
    {
        Score score = null;
        try {
            init();
            sql = "select * from score where score_id = %d";
            sql = String.format(sql,id);
            //执行，获得返回结果
            System.out.println(sql);
            ResultSet rs = stmt.executeQuery(sql);
            //使用结果
            if(rs.next())
            {
                //如果结果集不为空
                score = new Score();
                score.setScore_id(rs.getInt("score_id"));
                score.setStu_num(rs.getString("stu_num"));
                score.setStu_name(rs.getString("stu_name"));
                score.setStu_sex(rs.getString("stu_sex"));
                score.setStu_class(rs.getString("stu_class"));
                score.setCourse_name(rs.getString("course_name"));
                score.setScore_grade(rs.getDouble("score_grade"));
                score.setMajor(rs.getString("major"));
            }
            //关闭
            release();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return score;
    }

    public int getRecordCount(String stu_num, String stu_name)
    {
        int count = 0;
        try {
            init();
            sql = "select count(*) from score where 1 = 1 ";
            if(stu_num != null && !stu_num.equals(""))
            {
                sql += " and stu_num like '%" + stu_num + "%'";
            }
            if(stu_name != null && !stu_name.equals(""))
            {
                sql += " and stu_name like '%" + stu_name + "%'";
            }
            //执行，获得返回结果
            System.out.println(sql);
            ResultSet rs = stmt.executeQuery(sql);
            //使用结果
            if(rs.next())
            {
                count = rs.getInt(1);
            }
            //关闭
            release();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return count;
    }

    public int getRecordCount(String stu_name)
    {
        int count = 0;
        try {
            init();
            sql = "select count(*) from score where 1 = 1 ";
            if(stu_name != null && !stu_name.equals(""))
            {
                sql += " and stu_name like '%" + stu_name + "%'";
            }
            //执行，获得返回结果
            System.out.println(sql);
            ResultSet rs = stmt.executeQuery(sql);
            //使用结果
            if(rs.next())
            {
                count = rs.getInt(1);
            }
            //关闭
            release();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return count;
    }

    public void getList(PagerHelper<Score> pager)
    {
        try {
            init();
            sql = "select * from Score where 1 = 1";
            if(pager.getQueryParams().containsKey("stu_num"))
            {
                sql += " and stu_num like '%" + pager.getQueryParams().get("stu_num") + "%'";
            }
            if(pager.getQueryParams().containsKey("stu_name"))
            {
                sql += " and stu_name like '%" + pager.getQueryParams().get("stu_name") + "%'";
            }
            //拼接：limit
            sql += " limit " + (pager.getPageIndex() - 1) * pager.getPageSize() + "," + pager.getPageSize();//limit 3,3
            //执行，获得返回结果
            System.out.println(sql);
            ResultSet rs = stmt.executeQuery(sql);
            //使用结果
            List<Score> lst = new ArrayList<>();

            double sum=0.0;
            int count = getRecordCount(pager.getQueryParams().get("stu_num"),pager.getQueryParams().get("stu_name"));
            pager.setRecordCount(count);
            while(rs.next())
            {
                //如果结果集不为空
                Score score = new Score();
                score.setScore_id(rs.getInt("score_id"));
                score.setStu_num(rs.getString("stu_num"));
                score.setStu_name(rs.getString("stu_name"));
                score.setStu_sex(rs.getString("stu_sex"));
                score.setStu_class(rs.getString("stu_class"));
                score.setCourse_name(rs.getString("course_name"));
                score.setScore_grade(rs.getDouble("score_grade"));
                score.setMajor(rs.getString("major"));
                lst.add(score);
                if(count<new DBCourse().getCount())//课程的数量
                {
                    sum+=rs.getDouble("score_grade");
                }
            }
            //设置学生总成绩
            pager.setSumScore(sum);
            //设置学生平均成绩
            pager.setAvgScore(sum/count);
            System.out.println("sum:"+sum);
            pager.setData(lst);
            //关闭
            release();


        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public void getExcelData(PagerHelper<Score> pager)
    {
        try {
            init();
            sql = "select * from Score";
            ResultSet rs = stmt.executeQuery(sql);
            //使用结果
            List<Score> lst = new ArrayList<>();
            while(rs.next())
            {
                //如果结果集不为空
                Score score = new Score();
                score.setScore_id(rs.getInt("score_id"));
                score.setStu_num(rs.getString("stu_num"));
                score.setStu_name(rs.getString("stu_name"));
                score.setStu_sex(rs.getString("stu_sex"));
                score.setStu_class(rs.getString("stu_class"));
                score.setCourse_name(rs.getString("course_name"));
                score.setScore_grade(rs.getDouble("score_grade"));
                score.setMajor(rs.getString("major"));
                lst.add(score);
            }

            Map<String, Double> map = new HashMap<>();
            for(Score stu: lst){
                String name = stu.getStu_name();
                if(map.containsKey(name)){
                    map.put(name, map.get(name) + (stu.getScore_grade()));
                }else{
                    map.put(name, stu.getScore_grade());
                }
            }

            List<Score> list=new ArrayList<>();
            for (Map.Entry<String, Double> entry : map.entrySet()) {
                int count= getRecordCount(entry.getKey());
                String name=entry.getKey();
                double sumScore=entry.getValue();
                double avgScore=entry.getValue()/count;
                list.add(setData(name,sumScore,avgScore));
                //System.out.println("姓名：" + entry.getKey() + ", 总分数： " + sumScore+ ", 平均分： " + avgScore);
            }
            pager.setData(list);
            //关闭
            release();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Score setData(String stu_name,double sumScore,double avgScore)
    {
        Score score=null;
        try {
            init();
            sql = "select * from score where 1 = 1 ";
            if(stu_name != null && !stu_name.equals(""))
            {
                sql += " and stu_name like '%" + stu_name + "%'";
            }
            //执行，获得返回结果
           // System.out.println(sql);
            ResultSet rs = stmt.executeQuery(sql);

            while(rs.next())
            {
                score = new Score();
                score.setScore_id(rs.getInt("score_id"));
                score.setStu_num(rs.getString("stu_num"));
                score.setStu_name(rs.getString("stu_name"));
                score.setStu_sex(rs.getString("stu_sex"));
                score.setStu_class(rs.getString("stu_class"));
                score.setCourse_name(rs.getString("course_name"));
                score.setScore_grade(rs.getDouble("score_grade"));
                score.setMajor(rs.getString("major"));
                score.setSumScore(sumScore);
                score.setAvgScore(avgScore);
            }
            release();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return score;
    }


}
