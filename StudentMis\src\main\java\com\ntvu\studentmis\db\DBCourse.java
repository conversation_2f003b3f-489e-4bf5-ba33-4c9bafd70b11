package com.ntvu.studentmis.db;

import com.ntvu.studentmis.entity.Course;
import com.ntvu.studentmis.pager.PagerHelper;
import com.ntvu.studentmis.util.WebTools;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 对数据库操作的类
 */
public class DBCourse {
    //驱动类
    private final String driverClassName = "com.mysql.cj.jdbc.Driver";
    //连接数据库地址
    private final String url = "*****************************************";
    //连接数据库用户名
    private final String dbName = "root";
    //连接数据库密码
    private final String dbPwd = "2847";

    //
    private Connection conn = null;
    private Statement stmt = null;
    private String sql = null;
    private ResultSet rs = null;

    /**
     * 初始化数据连接
     */
    private void init(){
        try {
            //加载驱动
            Class.forName(driverClassName);
            //获得与数据库的连接
            this.conn = DriverManager.getConnection(url,dbName,dbPwd);
            //获得招待句柄
            this.stmt = this.conn.createStatement();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 释放连接资源
     */
    private void release()
    {
        //关闭
        try {
            if(rs != null && !rs.isClosed())
            {
                rs.close();
                rs = null;
            }
            if(stmt != null && !stmt.isClosed()) {
                stmt.close();
                stmt = null;
            }
            if(conn != null && !conn.isClosed()) {
                conn.close();
                conn = null;
            }
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }
    }

    public boolean register(Course course)
    {
        boolean succeed = false;
        try{
            init();
            sql = "insert into Course(course_name,course_credit,course_hours,course_teacher,coursedate)" +
                    " values(%s,%d,%d,%s,%s)";
            sql = String.format(sql,
                    "'" + course.getCourse_name() + "'",
                    course.getCourse_credit(),
                    course.getCourse_hours(),
                    "'" + course.getCourse_teacher() + "'",
                    "'" + course.getCoursedate() + "'");

            System.out.println(sql);
            //执行，返回受影响的记录行数
            int effectedRows = stmt.executeUpdate(sql);
            //使用结果
            //如果结果集不为空
            succeed = effectedRows > 0;
            //关闭
            release();
        }catch (Exception ex)
        {
            ex.printStackTrace();
        }
        return succeed;
    }

    public boolean delete(int id)
    {
        boolean succeed = false;
        try{
            init();
            sql = "delete from Course where course_id = %d";
            sql = String.format(sql, id);
            System.out.println(sql);
            //执行，返回受影响的记录行数
            int effectedRows = stmt.executeUpdate(sql);
            //使用结果
            if(effectedRows > 0)
            {
                //如果结果集不为空
                succeed = true;
            }else{
                succeed = false;
            }
            //关闭
            release();
        }catch (Exception ex)
        {
            ex.printStackTrace();
        }
        return succeed;
    }

    public boolean edit(Course course)
    {
        boolean succeed = false;
        try{
            init();
            sql = "update Course set course_name=%s,course_credit=%d,course_hours=%d,course_teacher=%s,coursedate=%s where course_id = %d";
            sql = String.format(sql,
                    "'" + course.getCourse_name() + "'",
                    course.getCourse_credit(),
                    course.getCourse_hours(),
                    "'" + course.getCourse_teacher() + "'",
                    "'" + course.getCoursedate() + "'",
                    course.getCourse_id());
            System.out.println(sql);
            //执行，返回受影响的记录行数
            int effectedRows = stmt.executeUpdate(sql);
            //使用结果
            //如果结果集不为空
            succeed = effectedRows > 0;
            //关闭
            release();
        }catch (Exception ex)
        {
            ex.printStackTrace();
        }
        return succeed;
    }

    //编辑删除
    public Course getListById(int id)
    {
        Course course = null;
        try {
            init();
            sql = "select * from course where course_id = %d";
            sql = String.format(sql,id);
            //执行，获得返回结果
            System.out.println(sql);
            ResultSet rs = stmt.executeQuery(sql);
            //使用结果
            if(rs.next())
            {
                //如果结果集不为空
                course = new Course();
                course.setCourse_id(rs.getInt("course_id"));
                course.setCourse_name(rs.getString("course_name"));
                course.setCourse_credit(rs.getInt("course_credit"));
                course.setCourse_hours(rs.getInt("course_hours"));
                course.setCourse_teacher(rs.getString("course_teacher"));
                course.setCoursedate(WebTools.dateToStr(rs.getDate("coursedate")));
            }
            //关闭
            release();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return course;
    }

    public int getRecordCount(String course_name)
    {
        int count = 0;
        try {
            init();
            sql = "select count(*) from course where 1 = 1 ";
            if(course_name != null && !course_name.equals(""))
            {
                sql += " and course_name like '%" + course_name + "%'";
            }
            //执行，获得返回结果
            System.out.println(sql);
            ResultSet rs = stmt.executeQuery(sql);
            //使用结果
            if(rs.next())
            {
                count = rs.getInt(1);
            }
            //关闭
            release();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return count;
    }


    public void getList(PagerHelper<Course> pager)
    {
        try {
            init();
            sql = "select * from Course where 1 = 1";
            if(pager.getQueryParams().containsKey("course_name"))
            {
                sql += " and course_name like '%" + pager.getQueryParams().get("course_name") + "%'";
            }
            //拼接：limit
            sql += " limit " + (pager.getPageIndex() - 1) * pager.getPageSize() + "," + pager.getPageSize();//limit 3,3
            //执行，获得返回结果
            System.out.println(sql);
            ResultSet rs = stmt.executeQuery(sql);
            //使用结果
            List<Course> lst = new ArrayList<>();
            //页码
            int count = getRecordCount(pager.getQueryParams().get("course_name"));
            pager.setRecordCount(count);
            while(rs.next())
            {
                //如果结果集不为空
                Course course = new Course();
                course.setCourse_id(rs.getInt("course_id"));
                course.setCourse_name(rs.getString("course_name"));
                course.setCourse_credit(rs.getInt("course_credit"));
                course.setCourse_hours(rs.getInt("course_hours"));
                course.setCourse_teacher(rs.getString("course_teacher"));
                course.setCoursedate(WebTools.dateToStr(rs.getDate("coursedate")));
                lst.add(course);
            }

            pager.setData(lst);
            //关闭
            release();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public int getCount()throws SQLException, ClassNotFoundException
    {
        init();
        String sql = "SELECT COUNT(*) count FROM Course";
        ResultSet resultSet = stmt.executeQuery(sql);
        if (resultSet.next())
        {
            int count= resultSet.getInt("count");
            release();
            return count;
        }
        release();
        return 0;
    }
}
