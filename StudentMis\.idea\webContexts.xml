<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="WebContextManager">
    <option name="state">
      <map>
        <entry key="file://$PROJECT_DIR$/src/main/webapp/admin/course/add.jsp" value="file://$PROJECT_DIR$/src/main/webapp/admin/course" />
        <entry key="file://$PROJECT_DIR$/src/main/webapp/admin/log/list.jsp" value="file://$PROJECT_DIR$/src/main/webapp/admin/log" />
        <entry key="file://$PROJECT_DIR$/src/main/webapp/admin/user/list.jsp" value="file://$PROJECT_DIR$/src/main/webapp/admin/user" />
        <entry key="file://$PROJECT_DIR$/src/main/webapp/include/header_nav.jsp" value="file://$PROJECT_DIR$/src/main/webapp/include" />
        <entry key="file://$PROJECT_DIR$/src/main/webapp/include/info.jsp" value="file://$PROJECT_DIR$/src/main/webapp/include" />
      </map>
    </option>
  </component>
</project>